/*
    FreeRTOS V9.0.0 - Copyright (C) 2016 Real Time Engineers Ltd.
    All rights reserved

    VISIT http://www.FreeRTOS.org TO ENSURE YOU ARE USING THE LATEST VERSION.

    This file is part of the FreeRTOS distribution.

    FreeRTOS is free software; you can redistribute it and/or modify it under
    the terms of the GNU General Public License (version 2) as published by the
    Free Software Foundation >>>> AND MODIFIED BY <<<< the FreeRTOS exception.

    ***************************************************************************
    >>!   NOTE: The modification to the GPL is included to allow you to     !<<
    >>!   distribute a combined work that includes FreeRTOS without being   !<<
    >>!   obliged to provide the source code for proprietary components     !<<
    >>!   outside of the FreeRTOS kernel.                                   !<<
    ***************************************************************************

    FreeRTOS is distributed in the hope that it will be useful, but WITHOUT ANY
    WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
    FOR A PARTICULAR PURPOSE.  Full license text is available on the following
    link: http://www.freertos.org/a00114.html

    ***************************************************************************
     *                                                                       *
     *    FreeRTOS provides completely free yet professionally developed,    *
     *    robust, strictly quality controlled, supported, and cross          *
     *    platform software that is more than just the market leader, it     *
     *    is the industry's de facto standard.                               *
     *                                                                       *
     *    Help yourself get started quickly while simultaneously helping     *
     *    to support the FreeRTOS project by purchasing a FreeRTOS           *
     *    tutorial book, reference manual, or both:                          *
     *    http://www.FreeRTOS.org/Documentation                              *
     *                                                                       *
    ***************************************************************************

    http://www.FreeRTOS.org/FAQHelp.html - Having a problem?  Start by reading
    the FAQ page "My application does not run, what could be wrong?".  Have you
    defined configASSERT()?

    http://www.FreeRTOS.org/support - In return for receiving this top quality
    embedded software for free we request you assist our global community by
    participating in the support forum.

    http://www.FreeRTOS.org/training - Investing in training allows your team to
    be as productive as possible as early as possible.  Now you can receive
    FreeRTOS training directly from Richard Barry, CEO of Real Time Engineers
    Ltd, and the world's leading authority on the world's leading RTOS.

    http://www.FreeRTOS.org/plus - A selection of FreeRTOS ecosystem products,
    including FreeRTOS+Trace - an indispensable productivity tool, a DOS
    compatible FAT file system, and our tiny thread aware UDP/IP stack.

    http://www.FreeRTOS.org/labs - Where new FreeRTOS products go to incubate.
    Come and try FreeRTOS+TCP, our new open source TCP/IP stack for FreeRTOS.

    http://www.OpenRTOS.com - Real Time Engineers ltd. license FreeRTOS to High
    Integrity Systems ltd. to sell under the OpenRTOS brand.  Low cost OpenRTOS
    licenses offer ticketed support, indemnification and commercial middleware.

    http://www.SafeRTOS.com - High Integrity Systems also provide a safety
    engineered and independently SIL3 certified version for use in safety and
    mission critical applications that require provable dependability.

    1 tab == 4 spaces!
*/

#ifndef FREERTOS_CONFIG_H
#define FREERTOS_CONFIG_H


#include "uart.h"

/* Cortex-A9 系统时钟定义 */
#define SystemCoreClock    500000000UL  /* 500MHz CPU时钟 */

/* FreeRTOS Tick中断函数声明 */
void vConfigureTickInterrupt(void);
void vClearTickInterrupt(void);

/*-----------------------------------------------------------
 * Application specific definitions.
 *
 * These definitions should be adjusted for your particular hardware and
 * application requirements.
 *
 * THESE PARAMETERS ARE DESCRIBED WITHIN THE 'CONFIGURATION' SECTION OF THE
 * FreeRTOS API DOCUMENTATION AVAILABLE ON THE FreeRTOS.org WEB SITE.
 *
 * See http://www.freertos.org/a00110.html.
 *----------------------------------------------------------*/

#define configUSE_PREEMPTION                    1   /* 抢占模式! */
#define configUSE_TIME_SLICING                  0   /* 时间片模式! */

#define configUSE_TICKLESS_IDLE                 0   /* 设置为 1 时，系统进入idle 状态后，关闭系统定时器（即不再产生 sys tick 中断）*/

#define configCPU_CLOCK_HZ                      (SystemCoreClock)
#define configTICK_RATE_HZ                      ((TickType_t)1000)

#define configMAX_PRIORITIES                    5
#define configMINIMAL_STACK_SIZE                ((unsigned short)1024)
#define configMAX_TASK_NAME_LEN                 20
#define configUSE_16_BIT_TICKS                  0
#define configIDLE_SHOULD_YIELD                 0
#define configUSE_TASK_NOTIFICATIONS            0
#define configUSE_MUTEXES                       0
#define configUSE_RECURSIVE_MUTEXES             0
#define configUSE_COUNTING_SEMAPHORES           0
#define configUSE_ALTERNATIVE_API               0 /* Deprecated! */
#define configQUEUE_REGISTRY_SIZE               8
#define configUSE_QUEUE_SETS                    0

#define configUSE_NEWLIB_REENTRANT              0
#define configENABLE_BACKWARD_COMPATIBILITY     0
#define configNUM_THREAD_LOCAL_STORAGE_POINTERS 5

/* Used memory allocation (heap_x.c) */
#define configFRTOS_MEMORY_SCHEME               4
/* Tasks.c additions (e.g. Thread Aware Debug capability) */
#define configINCLUDE_FREERTOS_TASK_C_ADDITIONS_H 0

/* Memory allocation related definitions. */
// 启用POSIX时需要将这里分开
// #define configSUPPORT_STATIC_ALLOCATION         0
// #define configSUPPORT_DYNAMIC_ALLOCATION        1
#define configTOTAL_HEAP_SIZE                   ((size_t)(50 * 1024))
#define configAPPLICATION_ALLOCATED_HEAP        0

/* Hook function related definitions. */
#define configUSE_IDLE_HOOK                     0
#define configUSE_TICK_HOOK                     0
#define configCHECK_FOR_STACK_OVERFLOW          0
#define configUSE_MALLOC_FAILED_HOOK            0
#define configUSE_DAEMON_TASK_STARTUP_HOOK      0

/* Run time and task stats gathering related definitions. */
#define configGENERATE_RUN_TIME_STATS           0
#define configUSE_TRACE_FACILITY                1
#define configUSE_STATS_FORMATTING_FUNCTIONS    1

/* Task aware debugging. */
#define configRECORD_STACK_HIGH_ADDRESS         1

/* Co-routine related definitions. */
#define configUSE_CO_ROUTINES                   0
#define configMAX_CO_ROUTINE_PRIORITIES         2

/* Software timer related definitions. */
// 使能POSIX是，需要打开定时器
#define configUSE_TIMERS                        0
#define configTIMER_TASK_PRIORITY               (configMAX_PRIORITIES - 1)
#define configTIMER_QUEUE_LENGTH                10
#define configTIMER_TASK_STACK_DEPTH            (configMINIMAL_STACK_SIZE * 2)

/* Define to trap errors during development. */
// #define configASSERT(x) if(( x) == 0) {break_point("in configAssert......\n"); taskDISABLE_INTERRUPTS(); for (;;);}

/* Optional functions - most linkers will remove unused functions anyway. */
#define INCLUDE_vTaskPrioritySet                1
#define INCLUDE_uxTaskPriorityGet               1
#define INCLUDE_vTaskDelete                     1
#define INCLUDE_vTaskSuspend                    1
#define INCLUDE_vTaskDelayUntil                 1
#define INCLUDE_vTaskDelay                      1
#define INCLUDE_xTaskGetSchedulerState          1
#define INCLUDE_xTaskGetCurrentTaskHandle       1
#define INCLUDE_uxTaskGetStackHighWaterMark     0
#define INCLUDE_xTaskGetIdleTaskHandle          0
// #define INCLUDE_eTaskGetState                   0
#define INCLUDE_xTimerPendFunctionCall          0
#define INCLUDE_xTaskAbortDelay                 0
#define INCLUDE_xTaskGetHandle                  0
#define INCLUDE_xTaskResumeFromISR              1


/* Cortex-A specific definitions. */
#define configMAX_API_CALL_INTERRUPT_PRIORITY 20

/* Cortex-A9 Private Timer Tick 中断设置 */
#define configSETUP_TICK_INTERRUPT()    vConfigureTickInterrupt()
#define configCLEAR_TICK_INTERRUPT()    vClearTickInterrupt()

/* Definitions that map the FreeRTOS port exception handlers to startup handler names. */
#define FreeRTOS_IRQ_Handler irq
#define FreeRTOS_SWI_Handler software_interrupt

/*
 * If define configUSE_TASK_FPU_SUPPORT to 1, the task will start without a floating
 * point context. A task that uses the floating point hardware must call vPortTaskUsesFPU()
 * before executing any floating point instructions.
 */
#ifndef configUSE_TASK_FPU_SUPPORT
#define configUSE_TASK_FPU_SUPPORT 1
#endif

/* GIC information defintions. */
#define configINTERRUPT_CONTROLLER_BASE_ADDRESS 0x3FFF0000UL
#define configINTERRUPT_CONTROLLER_CPU_INTERFACE_OFFSET 0x1000UL
#define configUNIQUE_INTERRUPT_PRIORITIES 32

/* ***************************************POSIX******************************************************** */
/* 动态分配和静态分配 */
    // 因为pthread_create需要，配置静态分配
    // 此处配置了静态分配后，task和timer中的静态分配函数已经在main.c中实现了，所以不需要再改动其内存分配条件
    #define configSUPPORT_STATIC_ALLOCATION 0
    
    // FreeRTOS默认支持动态分配，所以这里不配置，也是会支持动态分配
    #define configSUPPORT_DYNAMIC_ALLOCATION 1
/* phtread需要 */
    // 配置pthread需要用到的错误码和TASK_TAG
    #define configUSE_APPLICATION_TASK_TAG 1
    // #define configUSE_POSIX_ERRNO          1

    // 为了支持pthread_detach以期支持thread.detach，需要使能此宏
    #define INCLUDE_eTaskGetState 0

    // pthread中使用到了mutex，所以需要使能mutex，并且这里使能的具体类型是recursivemutex（暂时不知道其不同类型有什么区别）
    #define configUSE_MUTEXES           0
    #define configUSE_RECURSIVE_MUTEXES 0

    // pthread需要使用到有关函数，所以需要使能此处的宏
    #define INCLUDE_xQueueGetMutexHolder 0

    /* 开启 TLS 关键字支持 */
#define configUSE_TLS_KEYWORDS         0
    /* 开启计数信号量支持，condition_variable模块需要 */
#define configUSE_COUNTING_SEMAPHORES  0

/**************************************手动调试**********************************************************/
/* break point */
// 若启用断点，在freertos任务调度文件tasks.c每个函数中都有调试信息
#define BREAK_POINT 0

#if ( BREAK_POINT == 1 )
    #define break_point(info) myputs(info)
#endif

#if ( BREAK_POINT == 0 )
    #define break_point(info)
#endif


#endif /* FREERTOS_CONFIG_H */
